/**
 * 简化的使用示例 - 展示最常用的功能
 */

// 等待桥接准备
window.addEventListener('bridgeReady', async () => {
    console.log('桥接已准备就绪！');
    
    try {
        // 1. 发送HTTP请求
        const response = await Http.get('https://api.github.com/users/octocat');
        const userData = await response.json();
        console.log('用户数据:', userData);
        
        // 2. 获取设备信息
        const deviceInfo = await BridgeCore.invoke('device', 'getDeviceInfo');
        console.log('设备信息:', deviceInfo);
        
        // 3. 显示原生Toast
        await BridgeCore.invoke('ui', 'showToast', {
            message: '欢迎使用WebView Bridge！'
        });
        
        // 4. 保存数据到本地存储
        await BridgeCore.invoke('storage', 'save', {
            key: 'user_preference',
            value: JSON.stringify({ theme: 'dark', language: 'zh-CN' })
        });
        
        // 5. 监听网络状态变化
        BridgeCore.addEventListener('network.changed', (data) => {
            console.log('网络状态变化:', data);
            if (!data.connected) {
                alert('网络连接已断开！');
            }
        });
        
    } catch (error) {
        console.error('操作失败:', error);
    }
});

// 用户交互示例
function onButtonClick() {
    // 显示确认对话框
    BridgeCore.invoke('ui', 'showConfirm', {
        title: '确认操作',
        message: '确定要执行此操作吗？'
    }).then(result => {
        if (result.confirmed) {
            console.log('用户确认了操作');
            // 执行相应操作
        }
    }).catch(error => {
        console.error('显示对话框失败:', error);
    });
}

// 错误处理示例
function handleBridgeError(error) {
    console.error('桥接错误:', error);
    
    switch (error.type) {
        case 'TIMEOUT':
            alert('操作超时，请检查网络连接');
            break;
        case 'MODULE_NOT_FOUND':
            alert('功能模块未找到，请更新应用');
            break;
        case 'PERMISSION_DENIED':
            alert('权限不足，请检查应用权限设置');
            break;
        default:
            alert(`操作失败: ${error.message}`);
    }
}