<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebView Bridge Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        input, select {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebView Bridge 功能演示</h1>
        
        <!-- HTTP 请求演示 -->
        <div class="section">
            <h3>HTTP 请求模块</h3>
            <div>
                <input type="text" id="httpUrl" placeholder="请输入URL" value="https://jsonplaceholder.typicode.com/posts/1">
                <select id="httpMethod">
                    <option value="GET">GET</option>
                    <option value="POST">POST</option>
                    <option value="PUT">PUT</option>
                    <option value="DELETE">DELETE</option>
                </select>
                <button onclick="testHttpRequest()">发送请求</button>
                <button onclick="testHttpError()">测试错误处理</button>
            </div>
            <div id="httpResult" class="result"></div>
        </div>
        
        <!-- 设备信息演示 -->
        <div class="section">
            <h3>设备信息模块</h3>
            <button onclick="getDeviceInfo()">获取设备信息</button>
            <button onclick="getBatteryInfo()">获取电池信息</button>
            <button onclick="getNetworkInfo()">获取网络信息</button>
            <div id="deviceResult" class="result"></div>
        </div>
        
        <!-- UI 交互演示 -->
        <div class="section">
            <h3>UI 交互模块</h3>
            <button onclick="showToast()">显示Toast</button>
            <button onclick="showAlert()">显示Alert</button>
            <button onclick="showConfirm()">显示Confirm</button>
            <button onclick="vibrate()">震动</button>
            <div id="uiResult" class="result"></div>
        </div>
        
        <!-- 存储演示 -->
        <div class="section">
            <h3>存储模块</h3>
            <input type="text" id="storageKey" placeholder="存储键" value="test_key">
            <input type="text" id="storageValue" placeholder="存储值" value="test_value">
            <button onclick="saveData()">保存数据</button>
            <button onclick="loadData()">读取数据</button>
            <button onclick="removeData()">删除数据</button>
            <div id="storageResult" class="result"></div>
        </div>
        
        <!-- 事件监听演示 -->
        <div class="section">
            <h3>事件监听</h3>
            <button onclick="startListening()">开始监听事件</button>
            <button onclick="stopListening()">停止监听事件</button>
            <div id="eventResult" class="result"></div>
        </div>
        
        <!-- 调试信息 -->
        <div class="section">
            <h3>调试信息</h3>
            <button onclick="showDebugInfo()">显示调试信息</button>
            <button onclick="clearResults()">清空结果</button>
            <div id="debugResult" class="result"></div>
        </div>
    </div>

    <!-- 引入应用逻辑 -->
    <script src="app.js"></script>
</body>
</html>