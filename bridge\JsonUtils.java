package com.example.webview.bridge;

import android.util.Log;
import org.json.JSONObject;
import org.json.JSONException;

/**
 * JSON工具类
 * 提供安全的JSON解析和构建功能
 */
public class JsonUtils {
    private static final String TAG = "JsonUtils";
    
    /**
     * 安全解析JSON字符串
     * @param jsonStr JSON字符串
     * @return JSONObject对象，解析失败返回空对象
     */
    public JSONObject parseJsonSafely(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return new JSONObject();
        }
        try {
            return new JSONObject(jsonStr);
        } catch (JSONException e) {
            Log.w(TAG, "Failed to parse JSON: " + jsonStr, e);
            return new JSONObject();
        }
    }
    
    /**
     * 创建成功响应JSON
     * @param data 响应数据
     * @return 响应JSON对象
     */
    public JSONObject createSuccessResponse(JSONObject data) {
        try {
            JSONObject response = new JSONObject();
            response.put("success", true);
            response.put("data", data != null ? data : new JSONObject());
            response.put("timestamp", System.currentTimeMillis());
            return response;
        } catch (JSONException e) {
            Log.e(TAG, "Error creating success response", e);
            return new JSONObject();
        }
    }
    
    /**
     * 创建错误响应JSON
     * @param errorType 错误类型
     * @param message 错误消息
     * @return 错误JSON对象
     */
    public JSONObject createErrorResponse(String errorType, String message) {
        try {
            JSONObject response = new JSONObject();
            response.put("success", false);
            response.put("error", new JSONObject()
                .put("type", errorType)
                .put("message", message));
            response.put("timestamp", System.currentTimeMillis());
            return response;
        } catch (JSONException e) {
            Log.e(TAG, "Error creating error response", e);
            return new JSONObject();
        }
    }
}