/**
 * HTTP请求模块 - 基于桥接框架的HTTP封装
 */
;(function (window) {
  'use strict'

  /**
   * HTTP模块类
   */
  class HttpModule extends BridgeModule {
    constructor() {
      super('http')
    }

    /**
     * 发起HTTP请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<HttpResponse>}
     */
    async fetch(url, options = {}) {
      const { method = 'GET', headers = {}, body = null, timeout = 30000 } = options

      const params = {
        method: method.toUpperCase(),
        url,
        headers,
        body: body ? (typeof body === 'object' ? body : { data: body }) : null,
      }

      try {
        const responseData = await this.invoke('request', params, { timeout })
        return new HttpResponse(responseData)
      } catch (error) {
        throw new HttpError(error.message, error.type)
      }
    }

    // 便捷方法
    get(url, options = {}) {
      return this.fetch(url, { ...options, method: 'GET' })
    }

    post(url, options = {}) {
      return this.fetch(url, { ...options, method: 'POST' })
    }

    put(url, options = {}) {
      return this.fetch(url, { ...options, method: 'PUT' })
    }

    delete(url, options = {}) {
      return this.fetch(url, { ...options, method: 'DELETE' })
    }
  }

  /**
   * HTTP响应类
   */
  class HttpResponse {
    constructor(data) {
      this.status = data.status
      this.statusText = data.statusText
      this.ok = data.ok
      this.headers = data.headers
      this._body = data.body
    }

    async text() {
      return this._body || ''
    }

    async json() {
      try {
        return JSON.parse(this._body || '{}')
      } catch (error) {
        throw new Error('Invalid JSON response')
      }
    }
  }

  /**
   * HTTP错误类
   */
  class HttpError extends BridgeError {
    constructor(message, type = 'HTTP_ERROR') {
      super(message, type, 'http')
    }
  }

  // 创建全局实例
  window.Http = new HttpModule()
  window.HttpResponse = HttpResponse
  window.HttpError = HttpError
})(window)
