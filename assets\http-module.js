/**
 * HTTP请求模块 - 基于桥接框架的HTTP封装
 */
;(function (window) {
  'use strict'

  /**
   * HTTP模块类
   */
  class HttpModule extends BridgeModule {
    constructor() {
      super('http')
      this.activeRequests = new Map() // 跟踪活跃的请求
    }

    /**
     * 发起HTTP请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<HttpResponse>}
     */
    async fetch(url, options = {}) {
      const { method = 'GET', headers = {}, body = null, timeout = 30000, signal } = options

      const params = {
        method: method.toUpperCase(),
        url,
        headers,
        body: body ? (typeof body === 'object' ? body : { data: body }) : null,
      }

      // 创建可取消的请求
      const requestPromise = this.invoke('request', params, { timeout })

      // 如果提供了 AbortSignal，设置取消逻辑
      if (signal) {
        const abortHandler = () => {
          // 这里需要获取到 callbackId 来取消请求
          // 由于当前架构限制，我们先抛出取消错误
          throw new HttpError('Request aborted', 'ABORTED')
        }

        if (signal.aborted) {
          throw new HttpError('Request aborted', 'ABORTED')
        }

        signal.addEventListener('abort', abortHandler)

        try {
          const responseData = await requestPromise
          signal.removeEventListener('abort', abortHandler)
          return new HttpResponse(responseData)
        } catch (error) {
          signal.removeEventListener('abort', abortHandler)
          throw new HttpError(error.message, error.type)
        }
      } else {
        try {
          const responseData = await requestPromise
          return new HttpResponse(responseData)
        } catch (error) {
          throw new HttpError(error.message, error.type)
        }
      }
    }

    /**
     * 创建可取消的请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Object} 包含 promise 和 cancel 方法的对象
     */
    createCancelableRequest(url, options = {}) {
      const controller = new AbortController()
      const promise = this.fetch(url, { ...options, signal: controller.signal })

      return {
        promise,
        cancel: () => controller.abort(),
        signal: controller.signal,
      }
    }

    // 便捷方法
    get(url, options = {}) {
      return this.fetch(url, { ...options, method: 'GET' })
    }

    post(url, options = {}) {
      return this.fetch(url, { ...options, method: 'POST' })
    }

    put(url, options = {}) {
      return this.fetch(url, { ...options, method: 'PUT' })
    }

    delete(url, options = {}) {
      return this.fetch(url, { ...options, method: 'DELETE' })
    }
  }

  /**
   * HTTP响应类
   */
  class HttpResponse {
    constructor(data) {
      this.status = data.status
      this.statusText = data.statusText
      this.ok = data.ok
      this.headers = data.headers
      this._body = data.body
    }

    async text() {
      return this._body || ''
    }

    async json() {
      try {
        return JSON.parse(this._body || '{}')
      } catch (error) {
        throw new Error('Invalid JSON response')
      }
    }
  }

  /**
   * HTTP错误类
   */
  class HttpError extends BridgeError {
    constructor(message, type = 'HTTP_ERROR') {
      super(message, type, 'http')
    }
  }

  // 创建全局实例
  window.Http = new HttpModule()
  window.HttpResponse = HttpResponse
  window.HttpError = HttpError
})(window)
