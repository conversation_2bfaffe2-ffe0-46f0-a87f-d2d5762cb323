package com.example.webview.bridge;

import android.webkit.WebView;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import org.json.JSONObject;
import org.json.JSONException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 回调管理器
 * 专门负责管理JavaScript与Android之间的异步回调
 */
public class CallbackManager {
    private static final String TAG = "CallbackManager";
    
    private final WebView webView;
    private final Handler mainHandler;
    private final Map<String, BridgeCallback> callbackMap;
    
    public CallbackManager(WebView webView) {
        this.webView = webView;
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.callbackMap = new ConcurrentHashMap<>();
    }
    
    /**
     * 发送成功响应到JavaScript
     * @param callbackId 回调ID
     * @param data 响应数据
     */
    public void sendSuccess(String callbackId, JSONObject data) {
        if (callbackId == null) return;
        
        try {
            JSONObject response = new JSONObject();
            response.put("success", true);
            response.put("data", data != null ? data : new JSONObject());
            response.put("callbackId", callbackId);
            
            executeJavaScript(String.format("window.BridgeCore._handleCallback(%s);", response.toString()));
        } catch (JSONException e) {
            Log.e(TAG, "Error sending success response", e);
        }
    }
    
    /**
     * 发送错误响应到JavaScript
     * @param callbackId 回调ID
     * @param errorType 错误类型
     * @param message 错误消息
     */
    public void sendError(String callbackId, String errorType, String message) {
        if (callbackId == null) return;
        
        try {
            JSONObject response = new JSONObject();
            response.put("success", false);
            response.put("error", new JSONObject()
                .put("type", errorType)
                .put("message", message));
            response.put("callbackId", callbackId);
            
            executeJavaScript(String.format("window.BridgeCore._handleCallback(%s);", response.toString()));
        } catch (JSONException e) {
            Log.e(TAG, "Error sending error response", e);
        }
    }
    
    /**
     * 发送事件到JavaScript
     * @param eventName 事件名称
     * @param data 事件数据
     */
    public void sendEvent(String eventName, JSONObject data) {
        try {
            JSONObject event = new JSONObject();
            event.put("eventName", eventName);
            event.put("data", data != null ? data : new JSONObject());
            event.put("timestamp", System.currentTimeMillis());
            
            executeJavaScript(String.format("window.BridgeCore._handleEvent(%s);", event.toString()));
        } catch (JSONException e) {
            Log.e(TAG, "Error sending event", e);
        }
    }
    
    /**
     * 注册回调
     * @param callbackId 回调ID
     * @param callback 回调接口
     */
    public void registerCallback(String callbackId, BridgeCallback callback) {
        callbackMap.put(callbackId, callback);
    }
    
    /**
     * 移除回调
     * @param callbackId 回调ID
     */
    public void removeCallback(String callbackId) {
        callbackMap.remove(callbackId);
    }
    
    /**
     * 执行JavaScript代码
     * @param jsCode JavaScript代码
     */
    private void executeJavaScript(String jsCode) {
        mainHandler.post(() -> {
            if (webView != null) {
                webView.evaluateJavascript(jsCode, null);
            }
        });
    }
    
    /**
     * 清理所有回调
     */
    public void destroy() {
        callbackMap.clear();
    }
    
    /**
     * 回调接口
     */
    public interface BridgeCallback {
        void onSuccess(JSONObject data);
        void onError(String errorType, String message);
    }
}