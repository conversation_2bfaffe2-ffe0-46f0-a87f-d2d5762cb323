package com.example.webview.bridge;

import android.webkit.JavascriptInterface;
import android.webkit.WebView;
import android.util.Log;
import org.json.JSONObject;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 桥接管理器
 * 统一管理所有桥接模块，提供模块注册、路由和生命周期管理
 */
public class BridgeManager {
    private static final String TAG = "BridgeManager";

    private final WebView webView;
    private final Map<String, IBridge> bridgeModules;
    private final JsonUtils jsonUtils;
    private final CallbackManager callbackManager;

    public BridgeManager(WebView webView) {
        this.webView = webView;
        this.bridgeModules = new ConcurrentHashMap<>();
        this.jsonUtils = new JsonUtils();
        this.callbackManager = new CallbackManager(webView);

        initializeBridges();
    }

    /**
     * JavaScript调用入口
     * 
     * @param module     模块名称
     * @param method     方法名称
     * @param params     参数JSON字符串
     * @param callbackId 回调ID
     */
    @JavascriptInterface
    public final void invoke(String module, String method, String params, String callbackId) {
        try {
            // 解析参数
            JSONObject paramsObj = jsonUtils.parseJsonSafely(params);
            BridgeRequest request = new BridgeRequest(module, method, paramsObj, callbackId);

            // 路由到对应模块
            routeRequest(request);

        } catch (Exception e) {
            Log.e(TAG, "Error processing request", e);
            callbackManager.sendError(callbackId, "PARSE_ERROR", "Invalid request format");
        }
    }

    /**
     * 路由请求到对应模块
     * 
     * @param request 桥接请求
     */
    private void routeRequest(BridgeRequest request) {
        IBridge targetBridge = bridgeModules.get(request.getModule());

        if (targetBridge == null) {
            Log.w(TAG, "Bridge module not found: " + request.getModule());
            callbackManager.sendError(request.getCallbackId(), "MODULE_NOT_FOUND",
                    "Bridge module '" + request.getModule() + "' is not registered");
            return;
        }

        // 委托给具体模块处理
        if (targetBridge instanceof BaseBridge) {
            ((BaseBridge) targetBridge).processRequest(request);
        } else {
            targetBridge.handleRequest(request);
        }
    }

    /**
     * 初始化所有桥接模块
     */
    private void initializeBridges() {
        // 注册HTTP模块
        registerBridge(new HttpBridge(webView));

        // 注册设备信息模块
        registerBridge(new DeviceBridge(webView));

        // 注册UI模块
        registerBridge(new UIBridge(webView));

        // 注册存储模块
        registerBridge(new StorageBridge(webView));

        Log.d(TAG, "Initialized " + bridgeModules.size() + " bridge modules");
    }

    /**
     * 注册桥接模块
     * 
     * @param bridge 桥接模块实例
     */
    public void registerBridge(IBridge bridge) {
        String moduleName = bridge.getModuleName();
        bridgeModules.put(moduleName, bridge);
        bridge.initialize();
        Log.d(TAG, "Registered bridge module: " + moduleName);
    }

    /**
     * 注销桥接模块
     * 
     * @param moduleName 模块名称
     */
    public void unregisterBridge(String moduleName) {
        IBridge bridge = bridgeModules.remove(moduleName);
        if (bridge != null) {
            bridge.destroy();
            Log.d(TAG, "Unregistered bridge module: " + moduleName);
        }
    }

    /**
     * 获取已注册的模块列表
     * 
     * @return 模块名称数组
     */
    public String[] getRegisteredModules() {
        return bridgeModules.keySet().toArray(new String[0]);
    }

    /**
     * 检查模块是否已注册
     * 
     * @param moduleName 模块名称
     * @return 是否已注册
     */
    public boolean isModuleRegistered(String moduleName) {
        return bridgeModules.containsKey(moduleName);
    }

    /**
     * 清理所有资源
     */
    public void destroy() {
        Log.d(TAG, "Destroying BridgeManager");

        // 清理所有桥接模块
        for (IBridge bridge : bridgeModules.values()) {
            bridge.destroy();
        }
        bridgeModules.clear();

        // 清理回调管理器
        if (callbackManager != null) {
            callbackManager.destroy();
        }
    }

    /**
     * 主动向JavaScript发送事件
     * 
     * @param eventName 事件名称
     * @param data      事件数据
     */
    public void sendEventToJS(String eventName, JSONObject data) {
        callbackManager.sendEvent(eventName, data);
    }

    /**
     * 主动向JavaScript发送通知
     * 
     * @param title   通知标题
     * @param message 通知内容
     */
    public void sendNotificationToJS(String title, String message) {
        try {
            JSONObject data = new JSONObject();
            data.put("title", title);
            data.put("message", message);
            data.put("timestamp", System.currentTimeMillis());

            sendEventToJS("app.notification", data);
        } catch (Exception e) {
            Log.e(TAG, "Error sending notification to JS", e);
        }
    }

    /**
     * 发送网络状态变化事件
     * 
     * @param isConnected 是否连接
     * @param networkType 网络类型
     */
    public void sendNetworkStatusToJS(boolean isConnected, String networkType) {
        try {
            JSONObject data = new JSONObject();
            data.put("connected", isConnected);
            data.put("type", networkType);

            sendEventToJS("network.changed", data);
        } catch (Exception e) {
            Log.e(TAG, "Error sending network status to JS", e);
        }
    }
}
