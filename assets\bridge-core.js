/**
 * JavaScript桥接核心框架
 * 提供与Android原生功能交互的统一接口
 */
(function(window) {
    'use strict';
    
    // 回调计数器和映射表
    let callbackCounter = 0;
    const pendingCallbacks = new Map();
    const eventListeners = new Map();
    
    /**
     * 桥接错误类
     */
    class BridgeError extends Error {
        constructor(message, type = 'UNKNOWN_ERROR', module = null) {
            super(message);
            this.name = 'BridgeError';
            this.type = type;
            this.module = module;
        }
    }
    
    /**
     * 核心桥接类
     */
    class BridgeCore {
        /**
         * 调用Android原生方法
         * @param {string} module - 模块名称
         * @param {string} method - 方法名称
         * @param {Object} params - 参数对象
         * @param {Object} options - 选项配置
         * @returns {Promise}
         */
        static invoke(module, method, params = {}, options = {}) {
            return new Promise((resolve, reject) => {
                // 生成回调ID
                const callbackId = `callback_${++callbackCounter}_${Date.now()}`;
                
                // 设置超时
                const timeout = options.timeout || 30000;
                const timeoutId = setTimeout(() => {
                    pendingCallbacks.delete(callbackId);
                    reject(new BridgeError('Request timeout', 'TIMEOUT', module));
                }, timeout);
                
                // 存储回调
                pendingCallbacks.set(callbackId, {
                    resolve: (data) => {
                        clearTimeout(timeoutId);
                        pendingCallbacks.delete(callbackId);
                        resolve(data);
                    },
                    reject: (error) => {
                        clearTimeout(timeoutId);
                        pendingCallbacks.delete(callbackId);
                        reject(error);
                    }
                });
                
                // 调用Android方法
                try {
                    if (!window.AndroidBridge) {
                        throw new Error('Android Bridge not available');
                    }
                    
                    window.AndroidBridge.invoke(
                        module,
                        method,
                        JSON.stringify(params),
                        callbackId
                    );
                } catch (error) {
                    clearTimeout(timeoutId);
                    pendingCallbacks.delete(callbackId);
                    reject(new BridgeError(`Bridge error: ${error.message}`, 'BRIDGE_ERROR', module));
                }
            });
        }
        
        /**
         * 监听Android发送的事件
         * @param {string} eventName - 事件名称
         * @param {Function} listener - 事件监听器
         */
        static addEventListener(eventName, listener) {
            if (!eventListeners.has(eventName)) {
                eventListeners.set(eventName, new Set());
            }
            eventListeners.get(eventName).add(listener);
        }
        
        /**
         * 移除事件监听器
         * @param {string} eventName - 事件名称
         * @param {Function} listener - 事件监听器
         */
        static removeEventListener(eventName, listener) {
            const listeners = eventListeners.get(eventName);
            if (listeners) {
                listeners.delete(listener);
                if (listeners.size === 0) {
                    eventListeners.delete(eventName);
                }
            }
        }
        
        /**
         * 处理Android返回的回调（内部方法）
         * @param {Object} response - 响应数据
         */
        static _handleCallback(response) {
            const { callbackId, success, data, error } = response;
            const callback = pendingCallbacks.get(callbackId);
            
            if (callback) {
                if (success) {
                    callback.resolve(data);
                } else {
                    callback.reject(new BridgeError(error.message, error.type));
                }
            }
        }
        
        /**
         * 处理Android发送的事件（内部方法）
         * @param {Object} event - 事件数据
         */
        static _handleEvent(event) {
            const { eventName, data } = event;
            const listeners = eventListeners.get(eventName);
            
            if (listeners) {
                listeners.forEach(listener => {
                    try {
                        listener(data);
                    } catch (error) {
                        console.error('Error in event listener:', error);
                    }
                });
            }
        }
        
        /**
         * 获取待处理回调数量（调试用）
         */
        static getPendingCallbacksCount() {
            return pendingCallbacks.size;
        }
        
        /**
         * 清理所有待处理的回调
         */
        static clearPendingCallbacks() {
            pendingCallbacks.forEach(callback => {
                callback.reject(new BridgeError('Bridge destroyed', 'BRIDGE_DESTROYED'));
            });
            pendingCallbacks.clear();
        }
    }
    
    /**
     * 模块基类
     */
    class BridgeModule {
        constructor(moduleName) {
            this.moduleName = moduleName;
        }
        
        /**
         * 调用模块方法
         * @param {string} method - 方法名称
         * @param {Object} params - 参数
         * @param {Object} options - 选项
         * @returns {Promise}
         */
        invoke(method, params = {}, options = {}) {
            return BridgeCore.invoke(this.moduleName, method, params, options);
        }
        
        /**
         * 监听模块事件
         * @param {string} eventName - 事件名称
         * @param {Function} listener - 监听器
         */
        on(eventName, listener) {
            const fullEventName = `${this.moduleName}.${eventName}`;
            BridgeCore.addEventListener(fullEventName, listener);
        }
        
        /**
         * 移除模块事件监听
         * @param {string} eventName - 事件名称
         * @param {Function} listener - 监听器
         */
        off(eventName, listener) {
            const fullEventName = `${this.moduleName}.${eventName}`;
            BridgeCore.removeEventListener(fullEventName, listener);
        }
    }
    
    // 导出到全局
    window.BridgeCore = BridgeCore;
    window.BridgeModule = BridgeModule;
    window.BridgeError = BridgeError;
    
    // 页面卸载时清理资源
    window.addEventListener('beforeunload', () => {
        BridgeCore.clearPendingCallbacks();
    });
    
})(window);