package com.example.webview.bridge;

import android.webkit.JavascriptInterface;
import android.webkit.WebView;
import android.util.Log;
import org.json.JSONObject;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 基础桥接类
 * 提供桥接模块的通用功能和生命周期管理
 * 所有具体的桥接模块都应继承此类
 */
public abstract class BaseBridge implements IBridge {
    protected static final String TAG = "BaseBridge";
    
    protected final WebView webView;
    protected final CallbackManager callbackManager;
    protected final ExecutorService executorService;
    protected final JsonUtils jsonUtils;
    
    public BaseBridge(WebView webView) {
        this.webView = webView;
        this.callbackManager = new CallbackManager(webView);
        this.executorService = Executors.newCachedThreadPool();
        this.jsonUtils = new JsonUtils();
    }
    
    /**
     * JavaScript调用入口（由BridgeManager调用）
     * @param request 桥接请求
     */
    public final void processRequest(BridgeRequest request) {
        // 在后台线程处理请求，避免阻塞主线程
        executorService.execute(() -> {
            try {
                handleRequest(request);
            } catch (Exception e) {
                Log.e(TAG, "Error handling request: " + request, e);
                if (request.hasCallback()) {
                    callbackManager.sendError(request.getCallbackId(), "BRIDGE_ERROR", e.getMessage());
                }
            }
        });
    }
    
    /**
     * 发送成功响应
     * @param callbackId 回调ID
     * @param data 响应数据
     */
    protected void sendSuccess(String callbackId, JSONObject data) {
        callbackManager.sendSuccess(callbackId, data);
    }
    
    /**
     * 发送错误响应
     * @param callbackId 回调ID
     * @param errorType 错误类型
     * @param message 错误消息
     */
    protected void sendError(String callbackId, String errorType, String message) {
        callbackManager.sendError(callbackId, errorType, message);
    }
    
    /**
     * 发送事件
     * @param eventName 事件名称
     * @param data 事件数据
     */
    protected void sendEvent(String eventName, JSONObject data) {
        callbackManager.sendEvent(getModuleName() + "." + eventName, data);
    }
    
    @Override
    public void initialize() {
        Log.d(TAG, "Initializing bridge module: " + getModuleName());
    }
    
    @Override
    public void destroy() {
        Log.d(TAG, "Destroying bridge module: " + getModuleName());
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        if (callbackManager != null) {
            callbackManager.destroy();
        }
    }
}