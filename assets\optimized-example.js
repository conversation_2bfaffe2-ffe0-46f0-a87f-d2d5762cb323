/**
 * 优化后的桥接框架使用示例
 * 展示超时控制、请求取消、事件管理等新功能
 */

// 等待页面加载完成
document.addEventListener('DOMContentLoaded', async () => {
    console.log('=== 桥接框架优化功能演示 ===');
    
    try {
        // 1. 演示超时控制优化
        console.log('\n1. 超时控制演示:');
        
        // 短超时请求
        try {
            const quickResponse = await BridgeCore.invoke('device', 'getDeviceInfo', {}, { timeout: 5000 });
            console.log('快速请求成功:', quickResponse);
        } catch (error) {
            console.log('快速请求失败:', error.message);
        }
        
        // 2. 演示HTTP请求取消功能
        console.log('\n2. HTTP请求取消演示:');
        
        // 创建可取消的请求
        const cancelableRequest = Http.createCancelableRequest('https://httpbin.org/delay/5');
        
        // 2秒后取消请求
        setTimeout(() => {
            console.log('取消请求...');
            cancelableRequest.cancel();
        }, 2000);
        
        try {
            const response = await cancelableRequest.promise;
            console.log('请求完成:', response);
        } catch (error) {
            console.log('请求被取消或失败:', error.message);
        }
        
        // 3. 演示优化的事件监听
        console.log('\n3. 事件监听优化演示:');
        
        // 创建一个模块实例
        const deviceModule = new BridgeModule('device');
        
        // 使用返回的取消函数
        const unsubscribe = deviceModule.on('batteryChanged', (data) => {
            console.log('电池状态变化:', data);
        });
        
        // 一次性事件监听
        deviceModule.once('networkChanged', (data) => {
            console.log('网络状态变化（仅触发一次）:', data);
        });
        
        // 5秒后取消监听
        setTimeout(() => {
            console.log('取消电池状态监听');
            unsubscribe();
        }, 5000);
        
        // 4. 演示请求管理功能
        console.log('\n4. 请求管理演示:');
        
        console.log('当前活跃请求数量:', BridgeCore.getActiveRequestsCount());
        console.log('当前待处理回调数量:', BridgeCore.getPendingCallbacksCount());
        
        // 5. 演示批量HTTP请求
        console.log('\n5. 批量HTTP请求演示:');
        
        const urls = [
            'https://httpbin.org/json',
            'https://httpbin.org/uuid',
            'https://httpbin.org/user-agent'
        ];
        
        const requests = urls.map(url => Http.get(url, { timeout: 10000 }));
        
        try {
            const responses = await Promise.all(requests);
            console.log('批量请求完成:', responses.length, '个响应');
            
            for (let i = 0; i < responses.length; i++) {
                const data = await responses[i].json();
                console.log(`响应 ${i + 1}:`, data);
            }
        } catch (error) {
            console.log('批量请求失败:', error.message);
        }
        
        // 6. 演示错误处理
        console.log('\n6. 错误处理演示:');
        
        try {
            // 故意调用不存在的方法
            await BridgeCore.invoke('nonexistent', 'method');
        } catch (error) {
            console.log('预期的错误:', error.type, '-', error.message);
        }
        
        // 7. 演示资源清理
        console.log('\n7. 资源清理演示:');
        
        // 模拟页面卸载时的清理
        setTimeout(() => {
            console.log('清理所有资源...');
            deviceModule.removeAllListeners();
            BridgeCore.clearPendingCallbacks();
            console.log('清理完成');
        }, 8000);
        
    } catch (error) {
        console.error('演示过程中发生错误:', error);
    }
});

// 监听页面卸载事件
window.addEventListener('beforeunload', () => {
    console.log('页面即将卸载，清理资源...');
});

// 全局错误处理
window.addEventListener('error', (event) => {
    console.error('全局错误:', event.error);
});

// 未处理的Promise拒绝
window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
    event.preventDefault(); // 阻止默认的错误处理
});
