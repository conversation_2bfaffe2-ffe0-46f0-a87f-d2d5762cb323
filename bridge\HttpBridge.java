package com.example.webview.bridge;

import android.webkit.WebView;
import android.util.Log;
import org.json.JSONObject;
import org.json.JSONException;
import java.io.IOException;
import java.util.concurrent.TimeUnit;
import okhttp3.*;

/**
 * HTTP请求桥接模块
 * 为JavaScript提供HTTP请求功能
 */
public class HttpBridge extends BaseBridge {
    private static final String TAG = "HttpBridge";
    private static final String MODULE_NAME = "http";

    private OkHttpClient httpClient;

    public HttpBridge(WebView webView) {
        super(webView);
        initializeHttpClient();
    }

    @Override
    public String getModuleName() {
        return MODULE_NAME;
    }

    @Override
    public void initialize() {
        super.initialize();
        Log.d(TAG, "HTTP Bridge initialized");
    }

    @Override
    protected void handleRequest(BridgeRequest request) {
        switch (request.getMethod()) {
            case "request":
                handleHttpRequest(request);
                break;
            case "cancel":
                handleCancelRequest(request);
                break;
            default:
                sendError(request.getCallbackId(), "METHOD_NOT_FOUND",
                        "Method '" + request.getMethod() + "' not found in HTTP module");
        }
    }

    /**
     * 初始化HTTP客户端
     */
    private void initializeHttpClient() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
    }

    /**
     * 处理HTTP请求
     * 
     * @param request 桥接请求
     */
    private void handleHttpRequest(BridgeRequest request) {
        try {
            String method = request.getParam("method", "GET");
            String url = request.getParam("url", "");
            JSONObject headers = request.getParam("headers", new JSONObject());
            JSONObject body = request.getParam("body", null);

            if (url.isEmpty()) {
                sendError(request.getCallbackId(), "INVALID_URL", "URL cannot be empty");
                return;
            }

            // 构建HTTP请求
            Request.Builder requestBuilder = new Request.Builder().url(url);

            // 添加请求头
            addHeaders(requestBuilder, headers);

            // 设置请求体
            RequestBody requestBody = createRequestBody(method, body);
            requestBuilder.method(method.toUpperCase(), requestBody);

            Request httpRequest = requestBuilder.build();

            // 异步执行请求
            httpClient.newCall(httpRequest).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "HTTP request failed", e);
                    sendError(request.getCallbackId(), "NETWORK_ERROR", e.getMessage());
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        JSONObject responseData = buildResponseData(response);
                        sendSuccess(request.getCallbackId(), responseData);
                    } catch (Exception e) {
                        Log.e(TAG, "Error processing response", e);
                        sendError(request.getCallbackId(), "RESPONSE_ERROR", e.getMessage());
                    } finally {
                        response.close();
                    }
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error creating HTTP request", e);
            sendError(request.getCallbackId(), "REQUEST_ERROR", e.getMessage());
        }
    }

    /**
     * 处理取消请求
     * 
     * @param request 桥接请求
     */
    private void handleCancelRequest(BridgeRequest request) {
        // TODO: 实现请求取消逻辑
        sendSuccess(request.getCallbackId(), new JSONObject());
    }

    // 辅助方法

    private void addHeaders(Request.Builder builder, JSONObject headers) throws JSONException {
        if (headers == null)
            return;

        for (String key : headers.keys()) {
            builder.addHeader(key, headers.getString(key));
        }
    }

    private RequestBody createRequestBody(String method, JSONObject body) {
        if (body == null || "GET".equalsIgnoreCase(method) || "HEAD".equalsIgnoreCase(method)) {
            return null;
        }

        return RequestBody.create(body.toString(), MediaType.parse("application/json"));
    }

    private JSONObject buildResponseData(Response response) throws IOException, JSONException {
        JSONObject data = new JSONObject();
        data.put("status", response.code());
        data.put("statusText", response.message());
        data.put("ok", response.isSuccessful());

        // 响应头
        JSONObject responseHeaders = new JSONObject();
        for (String name : response.headers().names()) {
            responseHeaders.put(name, response.header(name));
        }
        data.put("headers", responseHeaders);

        // 响应体
        ResponseBody responseBody = response.body();
        if (responseBody != null) {
            data.put("body", responseBody.string());
        }

        return data;
    }

    @Override
    public void destroy() {
        super.destroy();
        if (httpClient != null) {
            httpClient.dispatcher().executorService().shutdown();
        }
    }
}
