package com.example.webview;

import android.app.Activity;
import android.os.Bundle;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.webkit.WebSettings;
import android.util.Log;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import com.example.webview.bridge.BridgeManager;

/**
 * WebView Activity - 展示如何集成和使用桥接框架
 */
public class WebViewActivity extends Activity {
    private static final String TAG = "WebViewActivity";

    private WebView webView;
    private BridgeManager bridgeManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_webview);

        initWebView();
        loadWebPage();
    }

    /**
     * 初始化WebView和桥接框架
     */
    private void initWebView() {
        webView = findViewById(R.id.webview);

        // 配置WebView设置
        configureWebViewSettings();

        // 创建并注册桥接管理器
        setupBridge();

        // 设置WebViewClient
        setupWebViewClient();
    }

    /**
     * 配置WebView基本设置
     */
    private void configureWebViewSettings() {
        WebSettings settings = webView.getSettings();

        // 启用JavaScript
        settings.setJavaScriptEnabled(true);

        // 启用DOM存储
        settings.setDomStorageEnabled(true);

        // 安全设置
        settings.setAllowFileAccess(false);
        settings.setAllowContentAccess(false);
        settings.setAllowFileAccessFromFileURLs(false);
        settings.setAllowUniversalAccessFromFileURLs(false);

        // 其他设置
        settings.setCacheMode(WebSettings.LOAD_DEFAULT);
        settings.setDatabaseEnabled(true);
        settings.setAppCacheEnabled(true);

        Log.d(TAG, "WebView settings configured");
    }

    /**
     * 设置桥接框架
     */
    private void setupBridge() {
        // 创建桥接管理器
        bridgeManager = new BridgeManager(webView);

        // 注册到WebView
        webView.addJavascriptInterface(bridgeManager, "AndroidBridge");

        Log.d(TAG, "Bridge framework initialized with modules: " +
                String.join(", ", bridgeManager.getRegisteredModules()));
    }

    /**
     * 设置WebViewClient
     */
    private void setupWebViewClient() {
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                Log.d(TAG, "Page started loading: " + url);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.d(TAG, "Page finished loading: " + url);

                // 页面加载完成后注入桥接JavaScript代码
                injectBridgeJavaScript();
            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                Log.e(TAG, "WebView error: " + description + " (Code: " + errorCode + ")");
            }
        });
    }

    /**
     * 注入桥接JavaScript代码
     */
    private void injectBridgeJavaScript() {
        try {
            // 注入核心桥接框架
            String coreJS = loadJSFromAssets("bridge-core.js");
            webView.evaluateJavascript(coreJS, result -> {
                Log.d(TAG, "Bridge core injected");
            });

            // 注入HTTP模块
            String httpJS = loadJSFromAssets("http-module.js");
            webView.evaluateJavascript(httpJS, result -> {
                Log.d(TAG, "HTTP module injected");
            });

            // 注入设备信息模块
            String deviceJS = loadJSFromAssets("device-module.js");
            webView.evaluateJavascript(deviceJS, result -> {
                Log.d(TAG, "Device module injected");
            });

            // 注入UI模块
            String uiJS = loadJSFromAssets("ui-module.js");
            webView.evaluateJavascript(uiJS, result -> {
                Log.d(TAG, "UI module injected");
            });

            // 触发桥接准备完成事件
            webView.evaluateJavascript(
                    "window.dispatchEvent(new Event('bridgeReady'));",
                    result -> Log.d(TAG, "Bridge ready event dispatched"));

        } catch (Exception e) {
            Log.e(TAG, "Error injecting bridge JavaScript", e);
        }
    }

    /**
     * 从assets目录加载JavaScript文件
     */
    private String loadJSFromAssets(String fileName) throws IOException {
        InputStream inputStream = getAssets().open(fileName);
        byte[] buffer = new byte[inputStream.available()];
        inputStream.read(buffer);
        inputStream.close();
        return new String(buffer, StandardCharsets.UTF_8);
    }

    /**
     * 加载网页
     */
    private void loadWebPage() {
        // 可以加载本地HTML文件或远程URL
        webView.loadUrl("file:///android_asset/index.html");
        // 或者加载远程URL
        // webView.loadUrl("https://your-web-app.com");
    }

    /**
     * 主动向JavaScript发送事件示例
     */
    public void sendEventToJS() {
        if (bridgeManager != null) {
            // 这里可以通过bridgeManager发送事件
            // 具体实现需要在BridgeManager中添加相应方法
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 清理桥接资源
        if (bridgeManager != null) {
            bridgeManager.destroy();
        }

        // 清理WebView
        if (webView != null) {
            webView.destroy();
        }

        Log.d(TAG, "Activity destroyed, resources cleaned up");
    }

    @Override
    public void onBackPressed() {
        // 处理返回键，支持网页后退
        if (webView != null && webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }
}
