package com.example.webview.bridge;

import org.json.JSONObject;

/**
 * 桥接请求数据模型
 * 封装从JavaScript传递过来的请求信息
 */
public class BridgeRequest {
    private final String module;
    private final String method;
    private final JSONObject params;
    private final String callbackId;
    private final long timestamp;
    
    public BridgeRequest(String module, String method, JSONObject params, String callbackId) {
        this.module = module;
        this.method = method;
        this.params = params != null ? params : new JSONObject();
        this.callbackId = callbackId;
        this.timestamp = System.currentTimeMillis();
    }
    
    public String getModule() {
        return module;
    }
    
    public String getMethod() {
        return method;
    }
    
    public JSONObject getParams() {
        return params;
    }
    
    public String getCallbackId() {
        return callbackId;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    /**
     * 检查是否有回调
     * @return 是否需要回调
     */
    public boolean hasCallback() {
        return callbackId != null && !callbackId.trim().isEmpty();
    }
    
    /**
     * 获取参数值
     * @param key 参数键
     * @param defaultValue 默认值
     * @return 参数值
     */
    public String getParam(String key, String defaultValue) {
        return params.optString(key, defaultValue);
    }
    
    public int getParam(String key, int defaultValue) {
        return params.optInt(key, defaultValue);
    }
    
    public boolean getParam(String key, boolean defaultValue) {
        return params.optBoolean(key, defaultValue);
    }
    
    public JSONObject getParam(String key, JSONObject defaultValue) {
        return params.optJSONObject(key) != null ? params.optJSONObject(key) : defaultValue;
    }
    
    @Override
    public String toString() {
        return String.format("BridgeRequest{module='%s', method='%s', callbackId='%s', timestamp=%d}", 
            module, method, callbackId, timestamp);
    }
}