/**
 * WebView Bridge 应用演示
 * 展示如何使用各种桥接功能
 */

// 全局变量
let eventListeners = [];

// 等待桥接准备完成
window.addEventListener('bridgeReady', () => {
    console.log('Bridge is ready!');
    showResult('debugResult', '桥接框架已准备就绪！', 'success');
    
    // 设置全局错误处理
    setupGlobalErrorHandling();
    
    // 初始化事件监听
    initializeEventListeners();
});

/**
 * 设置全局错误处理
 */
function setupGlobalErrorHandling() {
    window.addEventListener('error', (event) => {
        console.error('Global error:', event.error);
        showResult('debugResult', `全局错误: ${event.error.message}`, 'error');
    });
    
    window.addEventListener('unhandledrejection', (event) => {
        console.error('Unhandled promise rejection:', event.reason);
        showResult('debugResult', `未处理的Promise错误: ${event.reason}`, 'error');
    });
}

/**
 * 初始化事件监听器
 */
function initializeEventListeners() {
    // 监听网络状态变化
    BridgeCore.addEventListener('network.changed', (data) => {
        console.log('Network status changed:', data);
        showResult('eventResult', `网络状态变化: ${JSON.stringify(data, null, 2)}`, 'success');
    });
    
    // 监听应用通知
    BridgeCore.addEventListener('app.notification', (data) => {
        console.log('App notification:', data);
        showResult('eventResult', `应用通知: ${data.title} - ${data.message}`, 'success');
    });
}

// ==================== HTTP 请求演示 ====================

/**
 * 测试HTTP请求
 */
async function testHttpRequest() {
    const url = document.getElementById('httpUrl').value;
    const method = document.getElementById('httpMethod').value;
    
    if (!url) {
        showResult('httpResult', '请输入URL', 'error');
        return;
    }
    
    try {
        showResult('httpResult', '正在发送请求...', '');
        
        let response;
        const options = {
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'WebView-Bridge-Demo/1.0'
            },
            timeout: 10000
        };
        
        // 根据方法发送请求
        switch (method) {
            case 'GET':
                response = await Http.get(url, options);
                break;
            case 'POST':
                options.body = { title: 'Test Post', body: 'This is a test post', userId: 1 };
                response = await Http.post(url, options);
                break;
            case 'PUT':
                options.body = { id: 1, title: 'Updated Post', body: 'This is an updated post', userId: 1 };
                response = await Http.put(url, options);
                break;
            case 'DELETE':
                response = await Http.delete(url, options);
                break;
        }
        
        const data = await response.json();
        
        const result = {
            status: response.status,
            statusText: response.statusText,
            ok: response.ok,
            headers: response.headers,
            data: data
        };
        
        showResult('httpResult', JSON.stringify(result, null, 2), 'success');
        
    } catch (error) {
        console.error('HTTP request failed:', error);
        showResult('httpResult', `请求失败: ${error.message}\n类型: ${error.type}`, 'error');
    }
}

/**
 * 测试HTTP错误处理
 */
async function testHttpError() {
    try {
        showResult('httpResult', '测试错误处理...', '');
        
        // 请求一个不存在的URL
        const response = await Http.get('https://nonexistent-domain-12345.com/api/test', {
            timeout: 5000
        });
        
        showResult('httpResult', '意外成功了？', 'error');
        
    } catch (error) {
        console.log('Expected error caught:', error);
        showResult('httpResult', `错误处理测试成功:\n错误类型: ${error.type}\n错误信息: ${error.message}`, 'success');
    }
}

// ==================== 设备信息演示 ====================

/**
 * 获取设备信息
 */
async function getDeviceInfo() {
    try {
        showResult('deviceResult', '正在获取设备信息...', '');
        
        const deviceInfo = await BridgeCore.invoke('device', 'getDeviceInfo');
        showResult('deviceResult', JSON.stringify(deviceInfo, null, 2), 'success');
        
    } catch (error) {
        console.error('Get device info failed:', error);
        showResult('deviceResult', `获取设备信息失败: ${error.message}`, 'error');
    }
}

/**
 * 获取电池信息
 */
async function getBatteryInfo() {
    try {
        showResult('deviceResult', '正在获取电池信息...', '');
        
        const batteryInfo = await BridgeCore.invoke('device', 'getBatteryInfo');
        showResult('deviceResult', JSON.stringify(batteryInfo, null, 2), 'success');
        
    } catch (error) {
        console.error('Get battery info failed:', error);
        showResult('deviceResult', `获取电池信息失败: ${error.message}`, 'error');
    }
}

/**
 * 获取网络信息
 */
async function getNetworkInfo() {
    try {
        showResult('deviceResult', '正在获取网络信息...', '');
        
        const networkInfo = await BridgeCore.invoke('device', 'getNetworkInfo');
        showResult('deviceResult', JSON.stringify(networkInfo, null, 2), 'success');
        
    } catch (error) {
        console.error('Get network info failed:', error);
        showResult('deviceResult', `获取网络信息失败: ${error.message}`, 'error');
    }
}

// ==================== UI 交互演示 ====================

/**
 * 显示Toast
 */
async function showToast() {
    try {
        await BridgeCore.invoke('ui', 'showToast', {
            message: '这是一个Toast消息',
            duration: 'short'
        });
        
        showResult('uiResult', 'Toast已显示', 'success');
        
    } catch (error) {
        console.error('Show toast failed:', error);
        showResult('uiResult', `显示Toast失败: ${error.message}`, 'error');
    }
}

/**
 * 显示Alert
 */
async function showAlert() {
    try {
        const result = await BridgeCore.invoke('ui', 'showAlert', {
            title: '提示',
            message: '这是一个Alert对话框',
            buttonText: '确定'
        });
        
        showResult('uiResult', `Alert结果: ${JSON.stringify(result)}`, 'success');
        
    } catch (error) {
        console.error('Show alert failed:', error);
        showResult('uiResult', `显示Alert失败: ${error.message}`, 'error');
    }
}

/**
 * 显示Confirm
 */
async function showConfirm() {
    try {
        const result = await BridgeCore.invoke('ui', 'showConfirm', {
            title: '确认',
            message: '你确定要执行这个操作吗？',
            positiveText: '确定',
            negativeText: '取消'
        });
        
        showResult('uiResult', `Confirm结果: ${JSON.stringify(result)}`, 'success');
        
    } catch (error) {
        console.error('Show confirm failed:', error);
        showResult('uiResult', `显示Confirm失败: ${error.message}`, 'error');
    }
}

/**
 * 震动
 */
async function vibrate() {
    try {
        await BridgeCore.invoke('ui', 'vibrate', {
            duration: 500
        });
        
        showResult('uiResult', '震动已执行', 'success');
        
    } catch (error) {
        console.error('Vibrate failed:', error);
        showResult('uiResult', `震动失败: ${error.message}`, 'error');
    }
}

// ==================== 存储演示 ====================

/**
 * 保存数据
 */
async function saveData() {
    const key = document.getElementById('storageKey').value;
    const value = document.getElementById('storageValue').value;
    
    if (!key || !value) {
        showResult('storageResult', '请输入键和值', 'error');
        return;
    }
    
    try {
        await BridgeCore.invoke('storage', 'save', {
            key: key,
            value: value
        });
        
        showResult('storageResult', `数据已保存: ${key} = ${value}`, 'success');
        
    } catch (error) {
        console.error('Save data failed:', error);
        showResult('storageResult', `保存数据失败: ${error.message}`, 'error');
    }
}

/**
 * 读取数据
 */
async function loadData() {
    const key = document.getElementById('storageKey').value;
    
    if (!key) {
        showResult('storageResult', '请输入键', 'error');
        return;
    }
    
    try {
        const result = await BridgeCore.invoke('storage', 'load', {
            key: key
        });
        
        showResult('storageResult', `读取数据: ${key} = ${result.value || '(不存在)'}`, 'success');
        
    } catch (error) {
        console.error('Load data failed:', error);
        showResult('storageResult', `读取数据失败: ${error.message}`, 'error');
    }
}

/**
 * 删除数据
 */
async function removeData() {
    const key = document.getElementById('storageKey').value;
    
    if (!key) {
        showResult('storageResult', '请输入键', 'error');
        return;
    }
    
    try {
        await BridgeCore.invoke('storage', 'remove', {
            key: key
        });
        
        showResult('storageResult', `数据已删除: ${key}`, 'success');
        
    } catch (error) {
        console.error('Remove data failed:', error);
        showResult('storageResult', `删除数据失败: ${error.message}`, 'error');
    }
}

// ==================== 事件监听演示 ====================

/**
 * 开始监听事件
 */
function startListening() {
    // 监听自定义事件
    const customListener = (data) => {
        showResult('eventResult', `收到自定义事件: ${JSON.stringify(data)}`, 'success');
    };
    
    BridgeCore.addEventListener('custom.event', customListener);
    eventListeners.push({ name: 'custom.event', listener: customListener });
    
    showResult('eventResult', '开始监听事件...', 'success');
}

/**
 * 停止监听事件
 */
function stopListening() {
    eventListeners.forEach(({ name, listener }) => {
        BridgeCore.removeEventListener(name, listener);
    });
    eventListeners = [];
    
    showResult('eventResult', '已停止监听事件', 'success');
}

// ==================== 调试功能 ====================

/**
 * 显示调试信息
 */
function showDebugInfo() {
    const debugInfo = {
        pendingCallbacks: BridgeCore.getPendingCallbacksCount(),
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        bridgeAvailable: !!window.AndroidBridge,
        modules: {
            Http: !!window.Http,
            BridgeCore: !!window.BridgeCore,
            BridgeModule: !!window.BridgeModule
        }
    };
    
    showResult('debugResult', JSON.stringify(debugInfo, null, 2), 'success');
}

/**
 * 清空所有结果
 */
function clearResults() {
    const resultElements = document.querySelectorAll('.result');
    resultElements.forEach(element => {
        element.textContent = '';
        element.className = 'result';
    });
}

// ==================== 工具函数 ====================

/**
 * 显示结果
 * @param {string} elementId 元素ID
 * @param {string} message 消息内容
 * @param {string} type 消息类型 (success, error, '')
 */
function showResult(elementId, message, type = '') {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = message;
        element.className = `result ${type}`;
    }
    
    // 同时输出到控制台
    console.log(`[${elementId}] ${message}`);
}

/**
 * 格式化时间戳
 * @param {number} timestamp 时间戳
 * @returns {string} 格式化的时间字符串
 */
function formatTimestamp(timestamp) {
    return new Date(timestamp).toLocaleString();
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, waiting for bridge...');
    showResult('debugResult', '页面已加载，等待桥接框架...', '');
});