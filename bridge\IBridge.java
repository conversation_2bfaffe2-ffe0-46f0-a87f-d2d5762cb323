package com.example.webview.bridge;

import org.json.JSONObject;

/**
 * 桥接模块接口
 * 定义所有桥接模块必须实现的基本功能
 */
public interface IBridge {
    /**
     * 处理来自JavaScript的请求
     * @param request 桥接请求对象
     */
    void handleRequest(BridgeRequest request);
    
    /**
     * 获取模块名称
     * @return 模块名称
     */
    String getModuleName();
    
    /**
     * 模块初始化
     */
    void initialize();
    
    /**
     * 清理资源
     */
    void destroy();
}